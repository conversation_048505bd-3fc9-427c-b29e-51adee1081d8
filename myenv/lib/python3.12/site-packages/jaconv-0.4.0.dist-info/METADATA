Metadata-Version: 2.4
Name: jaconv
Version: 0.4.0
Summary: Pure-Python Japanese character interconverter for Hiragana, Katakana, Hankaku, Zenka<PERSON> and more
Home-page: https://github.com/ikegami-yukino/jaconv
Author: <PERSON><PERSON><PERSON>
Author-email: <EMAIL>
License: MIT License
Keywords: Japanese converter,Japanese,text preprocessing,half-width kana,Hiragana,Kataka<PERSON>,Hankaku,<PERSON><PERSON><PERSON>,transliteration,Julius
Platform: POSIX
Platform: Windows
Platform: Unix
Platform: MacOS
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: Information Technology
Classifier: License :: OSI Approved :: MIT License
Classifier: Natural Language :: Japanese
Classifier: Operating System :: MacOS
Classifier: Operating System :: Microsoft
Classifier: Operating System :: POSIX
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Text Processing
License-File: LICENSE
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: platform
Dynamic: summary

jaconv
==========
|coveralls| |pyversion| |version| |license| |download|

jaconv (Japanese Converter) is interconverter for Hiragana, Katakana, Hankaku (half-width character) and Zenkaku (full-width character)

`Japanese README <https://github.com/ikegami-yukino/jaconv/blob/master/README_JP.rst>`_ is available.

INSTALLATION
==============

::

 $ pip install jaconv


USAGE
============

See also `document <http://ikegami-yukino.github.io/jaconv/jaconv.html>`_

.. code:: python

  import jaconv

  # Hiragana to Katakana
  jaconv.hira2kata('ともえまみ')
  # => 'トモエマミ'

  # Hiragana to half-width Katakana
  jaconv.hira2hkata('ともえまみ')
  # => 'ﾄﾓｴﾏﾐ'

  # Katakana to Hiragana
  jaconv.kata2hira('巴マミ')
  # => '巴まみ'

  # half-width character to full-width character
  # default parameters are followings: kana=True, ascii=False, digit=False
  jaconv.h2z('ﾃｨﾛ･ﾌｨﾅｰﾚ')
  # => 'ティロ・フィナーレ'

  # half-width character to full-width character
  # but only ascii characters
  jaconv.h2z('abc', kana=False, ascii=True, digit=False)
  # => 'ａｂｃ'

  # half-width character to full-width character
  # but only digit characters
  jaconv.h2z('123', kana=False, ascii=False, digit=True)
  # => '１２３'

  # half-width character to full-width character
  # except half-width Katakana
  jaconv.h2z('ｱabc123', kana=False, digit=True, ascii=True)
  # => 'ｱａｂｃ１２３'

  # an alias of h2z
  jaconv.hankaku2zenkaku('ﾃｨﾛ･ﾌｨﾅｰﾚabc123')
  # => 'ティロ・フィナーレabc123'

  # full-width character to half-width character
  # default parameters are followings: kana=True, ascii=False, digit=False
  jaconv.z2h('ティロ・フィナーレ')
  # => 'ﾃｨﾛ・ﾌｨﾅｰﾚ'

  # full-width character to half-width character
  # but only ascii characters
  jaconv.z2h('ａｂｃ', kana=False, ascii=True, digit=False)
  # => 'abc'

  # full-width character to half-width character
  # but only digit characters
  jaconv.z2h('１２３', kana=False, ascii=False, digit=True)
  # => '123'

  # full-width character to half-width character
  # except full-width Katakana
  jaconv.z2h('アａｂｃ１２３', kana=False, digit=True, ascii=True)
  # => 'アabc123'

  # an alias of z2h
  jaconv.zenkaku2hankaku('ティロ・フィナーレａｂｃ１２３')
  # => 'ﾃｨﾛ･ﾌｨﾅｰﾚａｂｃ１２３'

  # normalize
  jaconv.normalize('ティロ･フィナ〜レ', 'NFKC')
  # => 'ティロ・フィナーレ'

  # Hiragana to alphabet
  jaconv.kana2alphabet('じゃぱん')
  # => 'japan'

  # Alphabet to Hiragana
  jaconv.alphabet2kana('japan')
  # => 'じゃぱん'

  # Katakana to Alphabet
  jaconv.kata2alphabet('ケツイ')
  # => 'ketsui'

  # Alphabet to Katakana
  jaconv.alphabet2kata('namba')
  # => 'ナンバ'

  # Hiragana to Julius's phoneme format
  jaconv.hiragana2julius('てんきすごくいいいいいい')
  # => 't e N k i s u g o k u i:'


NOTE
============

jaconv.normalize method expand unicodedata.normalize for Japanese language processing.

.. code::

    '〜' => 'ー'
    '～' => 'ー'
    "’" => "'"
    '”'=> '"'
    '“' => '``'
    '―' => '-'
    '‐' => '-'
    '˗' => '-'
    '֊' => '-'
    '‐' => '-'
    '‑' => '-'
    '‒' => '-'
    '–' => '-'
    '⁃' => '-'
    '⁻' => '-'
    '₋' => '-'
    '−' => '-'
    '﹣' => 'ー'
    '－' => 'ー'
    '—' => 'ー'
    '―' => 'ー'
    '━' => 'ー'
    '─' => 'ー'




.. |coveralls| image:: https://coveralls.io/repos/ikegami-yukino/jaconv/badge.svg?branch=master&service=github
    :target: https://coveralls.io/github/ikegami-yukino/jaconv?branch=master
    :alt: coveralls.io

.. |pyversion| image:: https://img.shields.io/pypi/pyversions/jaconv.svg

.. |version| image:: https://img.shields.io/pypi/v/jaconv.svg
    :target: http://pypi.python.org/pypi/jaconv/
    :alt: latest version

.. |license| image:: https://img.shields.io/pypi/l/jaconv.svg
    :target: http://pypi.python.org/pypi/jaconv/
    :alt: license

.. |download| image:: https://static.pepy.tech/personalized-badge/neologdn?period=total&units=international_system&left_color=black&right_color=blue&left_text=Downloads
    :target: https://pepy.tech/project/neologdn
    :alt: download


CHANGES
=======

0.4.0 (2024-7-26)
-------------------
- Add stub files according to PEP 561 for mypy (thanks @ernix)

0.3.4 (2023-2-18)
-------------------
- Fix to support Python2.7 ~ 3.4 (thanks @manjuu-eater)
- Support Python 3.11

0.3.3 (2022-12-31)
-------------------
- Support Python 3.10
- Re-support Python2.7 ~ 3.4 (thanks @manjuu-eater)
- Fix z2h, h2z all flag off bug (thanks @manjuu-eater)

0.3.1 (2022-12-14)
-------------------
- Fix alpha2kana infinite loop bug (thanks @frog42)

0.3 (2021-03-29)
-------------------
- Fix bug (alphabet2kana) thanks @Cuddlemuffin007
- Support Python 3.8 and 3.9
- Add handy functions: alphabet2kata and kata2alphabet. thanks @kokimame
- Add function for julius: hiragana2julius

0.2.4 (2018-02-04)
-------------------
- Fix bug (kana2alphabet)
- Support Python 3.7
- No longer support Python 2.6
- Add aliases of z2h -> zenkaku2hankaku and h2z -> hankaku2zenkaku

0.2.3 (2018-02-03)
-------------------
- Fix bugs (alphabet2kana, kana2alphabet) thanks @letuananh

0.2.2 (2018-01-22)
-------------------
- Fix bug (kana2alphabet) thanks @kokimame
- Support Python 3.6

0.2.1 (2017-09-14)
-------------------
- Fix bugs (alphabet2kana, kana2alphabet)

0.2 (2015-04-02)
------------------

- Change module name jctconv -> jaconv
- Add alphabet and hiragana interconvert (alphabet2kana, kana2alphabet)

0.1.1 (2015-03-12)
------------------

- Support Windows
- Support Python 3.5


0.1 (2014-11-24)
------------------

- Add some Japanese characters to convert table (ゝゞ・「」。、)
- Decresing memory usage
- Some function names are deprecated (hankaku2zenkaku, zenkaku2hankaku, H2K, H2hK, K2H)


0.0.7 (2014-03-22)
------------------

z2h and h2z allow mojimoji-like target character type determination.
Bug fix about Half Kana conversion.

