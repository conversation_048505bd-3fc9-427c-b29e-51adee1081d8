CHANGES
=======

0.4.0 (2024-7-26)
-------------------
- Add stub files according to PEP 561 for mypy (thanks @ernix)

0.3.4 (2023-2-18)
-------------------
- Fix to support Python2.7 ~ 3.4 (thanks @manjuu-eater)
- Support Python 3.11

0.3.3 (2022-12-31)
-------------------
- Support Python 3.10
- Re-support Python2.7 ~ 3.4 (thanks @manjuu-eater)
- Fix z2h, h2z all flag off bug (thanks @manjuu-eater)

0.3.1 (2022-12-14)
-------------------
- Fix alpha2kana infinite loop bug (thanks @frog42)

0.3 (2021-03-29)
-------------------
- Fix bug (alphabet2kana) thanks @C<PERSON><PERSON><PERSON>in007
- Support Python 3.8 and 3.9
- Add handy functions: alphabet2<PERSON> and kata2alphabet. thanks @kokimame
- Add function for julius: hiragana2julius

0.2.4 (2018-02-04)
-------------------
- Fix bug (kana2alphabet)
- Support Python 3.7
- No longer support Python 2.6
- Add aliases of z2h -> zenkaku2hankaku and h2z -> hankaku2zenkaku

0.2.3 (2018-02-03)
-------------------
- Fix bugs (alphabet2kana, kana2alphabet) thanks @letuananh

0.2.2 (2018-01-22)
-------------------
- Fix bug (kana2alphabet) thanks @kokimame
- Support Python 3.6

0.2.1 (2017-09-14)
-------------------
- Fix bugs (alphabet2kana, kana2alphabet)

0.2 (2015-04-02)
------------------

- Change module name jctconv -> jaconv
- Add alphabet and hiragana interconvert (alphabet2kana, kana2alphabet)

0.1.1 (2015-03-12)
------------------

- Support Windows
- Support Python 3.5


0.1 (2014-11-24)
------------------

- Add some Japanese characters to convert table (ゝゞ・「」。、)
- Decresing memory usage
- Some function names are deprecated (hankaku2zenkaku, zenkaku2hankaku, H2K, H2hK, K2H)


0.0.7 (2014-03-22)
------------------

z2h and h2z allow mojimoji-like target character type determination.
Bug fix about Half Kana conversion.

