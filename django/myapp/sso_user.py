# yourapp/utils/sso.py
from functools import wraps
from django.shortcuts import redirect
from django.http import HttpResponse

def sso_login_required(view_func):
    @wraps(view_func)
    def _wrapped(request, *args, **kwargs):
        if not request.session.get('sso_user'):
            # Nếu muốn trả 401 JSON cho API, đổi thành JsonResponse(...)
            return HttpResponse(f"Please log in to continue", status=400)
        return view_func(request, *args, **kwargs)
    return _wrapped
