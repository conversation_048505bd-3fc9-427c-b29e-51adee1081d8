# Generated by Django 5.2.1 on 2025-06-09 10:19

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
            ],
            options={
                'verbose_name_plural': 'Categories',
                'db_table': 'categories',
            },
        ),
        migrations.CreateModel(
            name='ProductItemMapping',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_item_master_id', models.CharField(max_length=100)),
                ('product_items_id', models.TextField(max_length=100)),
                ('sync_type', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('deleted_at', models.DateTimeField()),
                ('is_deleted', models.BooleanField()),
            ],
            options={
                'verbose_name_plural': 'ProductItemMapping',
                'db_table': 'product_item_mappings',
            },
        ),
        migrations.CreateModel(
            name='ProductItemMaster',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_code', models.CharField(max_length=15, unique=True)),
                ('parent_product_code', models.TextField(max_length=60)),
                ('product_name', models.CharField(max_length=255)),
                ('product_name_kana', models.CharField(max_length=255)),
                ('finet_product_name_kana', models.CharField(max_length=255)),
                ('product_rank', models.CharField(max_length=10)),
                ('capacity', models.FloatField()),
                ('capacity_ml', models.CharField(max_length=10)),
                ('unit', models.CharField(max_length=10)),
                ('quantity_per_case', models.IntegerField()),
                ('category_large', models.IntegerField()),
                ('category_middle', models.IntegerField()),
                ('category_small', models.IntegerField()),
                ('product_group_name', models.CharField(max_length=100)),
                ('foodnavi_product_code', models.CharField(max_length=20)),
                ('input_category_code', models.IntegerField()),
                ('input_category_name', models.CharField(max_length=100)),
                ('maker_code', models.IntegerField()),
                ('maker_name', models.CharField(max_length=100)),
                ('supplier_code_1', models.IntegerField()),
                ('supplier_name_1', models.CharField(max_length=100)),
                ('order_unit_1', models.IntegerField()),
                ('combined_1', models.IntegerField()),
                ('jan_code', models.IntegerField()),
                ('official_flg', models.CharField(max_length=200)),
                ('product_catchcopy', models.IntegerField()),
            ],
            options={
                'verbose_name_plural': 'ProductItemMaster',
                'db_table': 'product_item_master',
            },
        ),
        migrations.CreateModel(
            name='ProductItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_code', models.CharField(max_length=3)),
                ('product_name', models.CharField(max_length=200)),
                ('product_kana', models.CharField(max_length=200)),
                ('product_standard', models.CharField(max_length=20)),
                ('in_numbers', models.DecimalField(decimal_places=2, default=0.2, max_digits=15)),
                ('product_packing', models.CharField(max_length=20)),
                ('unit_name', models.CharField(max_length=10)),
                ('category_code1', models.CharField(max_length=12)),
                ('category_code2', models.CharField(max_length=12)),
                ('category_code3', models.CharField(max_length=12)),
                ('category_code4', models.CharField(max_length=12)),
                ('category_code5', models.CharField(max_length=12)),
                ('description1', models.TextField(null=True)),
                ('description2', models.TextField(null=True)),
                ('price_switch_day', models.DateField(default='2050-12-31')),
                ('rank_category_1', models.IntegerField(default=0)),
                ('rank_category_2', models.IntegerField(default=0)),
                ('lead_time', models.IntegerField()),
                ('measurement_division', models.IntegerField(default=0)),
                ('decimal_point_permission_division', models.IntegerField(default=0)),
                ('tax_type', models.IntegerField(default=0)),
                ('tax_rate', models.IntegerField(default=0)),
                ('is_visible', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField()),
                ('created_user', models.CharField(max_length=20)),
                ('updated_at', models.DateTimeField()),
                ('updated_user', models.CharField(max_length=20)),
                ('maker_name', models.CharField(default='', max_length=100)),
                ('product_name_search', models.CharField(default='', max_length=200)),
                ('product_kana_search', models.CharField(default='', max_length=200)),
                ('is_check_sync', models.BooleanField(default=True)),
                ('check_sync_at', models.DateTimeField(blank=True, null=True)),
                ('product_code', models.ForeignKey(db_column='product_code', on_delete=django.db.models.deletion.CASCADE, related_name='product_items', to='myapp.productitemmaster', to_field='product_code')),
            ],
            options={
                'db_table': 'product_items',
            },
        ),
    ]
