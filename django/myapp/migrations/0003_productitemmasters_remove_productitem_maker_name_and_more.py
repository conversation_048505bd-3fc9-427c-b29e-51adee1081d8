# Generated by Django 5.2.1 on 2025-06-13 09:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('myapp', '0002_alter_productitemmaster_jan_code'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductItemMasters',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_code', models.Char<PERSON>ield(max_length=20, unique=True)),
                ('parent_product_code', models.Char<PERSON>ield(blank=True, max_length=20, null=True)),
                ('product_name', models.Char<PERSON>ield(max_length=255)),
                ('product_name_kana', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('product_name_for_finet_kana', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('product_rank', models.Char<PERSON>ield(blank=True, max_length=50, null=True)),
                ('capacity', models.Char<PERSON>ield(blank=True, max_length=20, null=True)),
                ('capacity_ml', models.CharField(blank=True, max_length=20, null=True)),
                ('unit', models.CharField(blank=True, max_length=50, null=True)),
                ('number_of_units_per_pack', models.CharField(blank=True, max_length=50, null=True)),
                ('major_category', models.CharField(blank=True, max_length=100, null=True)),
                ('middle_category', models.CharField(blank=True, max_length=100, null=True)),
                ('subcategory', models.CharField(blank=True, max_length=100, null=True)),
                ('product_group_name', models.CharField(blank=True, max_length=100, null=True)),
                ('food_navi_product_code', models.CharField(blank=True, max_length=20, null=True)),
                ('input_classification_code', models.CharField(blank=True, max_length=20, null=True)),
                ('input_classification_name', models.CharField(blank=True, max_length=20, null=True)),
                ('manufacturer_code', models.CharField(blank=True, max_length=20, null=True)),
                ('manufacturer_name', models.CharField(blank=True, max_length=255, null=True)),
                ('supplier_code_1', models.CharField(blank=True, max_length=20, null=True)),
                ('supplier_1', models.CharField(blank=True, max_length=255, null=True)),
                ('order_unit_1', models.CharField(blank=True, max_length=100, null=True)),
                ('set_1', models.CharField(blank=True, max_length=100, null=True)),
                ('supplier_code_2', models.CharField(blank=True, max_length=20, null=True)),
                ('supplier_name_2', models.CharField(blank=True, max_length=255, null=True)),
                ('order_unit_2', models.CharField(blank=True, max_length=100, null=True)),
                ('set_2', models.CharField(blank=True, max_length=100, null=True)),
                ('supplier', models.CharField(blank=True, max_length=255, null=True)),
                ('transaction_partner_code', models.CharField(blank=True, max_length=100, null=True)),
                ('transaction_partner_name', models.CharField(blank=True, max_length=255, null=True)),
                ('empty_container_supplier_code', models.CharField(blank=True, max_length=100, null=True)),
                ('tax_code', models.CharField(blank=True, max_length=20, null=True)),
                ('tax_name', models.CharField(blank=True, max_length=255, null=True)),
                ('handling_category', models.CharField(blank=True, max_length=100, null=True)),
                ('handling_category_name', models.CharField(blank=True, max_length=255, null=True)),
                ('miscellaneous_category', models.CharField(blank=True, max_length=100, null=True)),
                ('miscellaneous_category_name', models.CharField(blank=True, max_length=255, null=True)),
                ('market_price_product_category', models.CharField(blank=True, max_length=100, null=True)),
                ('market_price_product_category_name', models.CharField(blank=True, max_length=255, null=True)),
                ('container_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('logistics_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('variable_weight_product_category', models.CharField(blank=True, max_length=100, null=True)),
                ('variable_weight_product_category_name', models.CharField(blank=True, max_length=255, null=True)),
                ('stocked_item_start_date', models.CharField(blank=True, max_length=20, null=True)),
                ('stocked_item_end_date', models.CharField(blank=True, max_length=20, null=True)),
                ('jan_code_presence_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('jan_code_presence_classification_name', models.CharField(blank=True, max_length=255, null=True)),
                ('case_jan_code', models.CharField(blank=True, max_length=25, null=True)),
                ('individual_item_jan_code', models.CharField(blank=True, max_length=25, null=True)),
                ('itf_code', models.CharField(blank=True, max_length=20, null=True)),
                ('sdp_code', models.CharField(blank=True, max_length=20, null=True)),
                ('jan_code_for_intage_use', models.CharField(blank=True, max_length=100, null=True)),
                ('jan_code_switch_date', models.CharField(blank=True, max_length=20, null=True)),
                ('old_case_jan_code', models.CharField(blank=True, max_length=100, null=True)),
                ('old_individual_item_jan_code', models.CharField(blank=True, max_length=100, null=True)),
                ('weight_per_case', models.CharField(blank=True, max_length=100, null=True)),
                ('individual_item_weight', models.CharField(blank=True, max_length=100, null=True)),
                ('warehouse_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('warehouse_classification_name', models.CharField(blank=True, max_length=255, null=True)),
                ('storage_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('storage_classification_name', models.CharField(blank=True, max_length=255, null=True)),
                ('location', models.CharField(blank=True, max_length=255, null=True)),
                ('case_depth', models.CharField(blank=True, max_length=100, null=True)),
                ('case_width', models.CharField(blank=True, max_length=100, null=True)),
                ('case_height', models.CharField(blank=True, max_length=100, null=True)),
                ('individual_item_depth', models.CharField(blank=True, max_length=100, null=True)),
                ('individual_item_width', models.CharField(blank=True, max_length=100, null=True)),
                ('individual_item_height', models.CharField(blank=True, max_length=100, null=True)),
                ('case_rollover_permission_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('case_rollover_permission_classification_name', models.CharField(blank=True, max_length=255, null=True)),
                ('empty_container_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('empty_container_classification_name', models.CharField(blank=True, max_length=255, null=True)),
                ('returnable_empty_container_unit_price_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('returnable_empty_container_unit_price_classification_name', models.CharField(blank=True, max_length=255, null=True)),
                ('deposit_code', models.CharField(blank=True, max_length=100, null=True)),
                ('deposit_name', models.CharField(blank=True, max_length=255, null=True)),
                ('expiry_date', models.CharField(blank=True, max_length=100, null=True)),
                ('shipment_deadline', models.CharField(blank=True, max_length=100, null=True)),
                ('shipment_deadline_warning_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('shipment_deadline_warning_classification_name', models.CharField(blank=True, max_length=255, null=True)),
                ('long_term_restocking_uncertain_flag', models.CharField(blank=True, max_length=5, null=True)),
                ('discontinuation_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('discontinuation_classification_name', models.CharField(blank=True, max_length=255, null=True)),
                ('discontinuation_year_month', models.CharField(blank=True, max_length=20, null=True)),
                ('discontinuation_date', models.CharField(blank=True, max_length=100, null=True)),
                ('discontinuation_timing_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('reason_for_discontinuation', models.TextField(blank=True, null=True)),
                ('discontinuation_status_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('discontinuation_status_classification_name', models.CharField(blank=True, max_length=255, null=True)),
                ('substitute_product_code', models.CharField(blank=True, max_length=100, null=True)),
                ('substitute_product_name', models.CharField(blank=True, max_length=255, null=True)),
                ('recommended_product_code_1', models.CharField(blank=True, max_length=100, null=True)),
                ('recommended_product_name_1', models.CharField(blank=True, max_length=255, null=True)),
                ('recommended_product_code_2', models.CharField(blank=True, max_length=100, null=True)),
                ('recommended_product_name_2', models.CharField(blank=True, max_length=255, null=True)),
                ('recommended_product_code_3', models.CharField(blank=True, max_length=100, null=True)),
                ('recommended_product_name_3', models.CharField(blank=True, max_length=255, null=True)),
                ('sales_recommendation_level', models.CharField(blank=True, max_length=20, null=True)),
                ('discontinued_product_code', models.CharField(blank=True, max_length=20, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('alcohol_content', models.CharField(blank=True, max_length=100, null=True)),
                ('extract_content', models.CharField(blank=True, max_length=100, null=True)),
                ('contract_condition_1', models.TextField(blank=True, null=True)),
                ('contract_condition_2', models.TextField(blank=True, null=True)),
                ('contract_validity_period_from', models.CharField(blank=True, max_length=20, null=True)),
                ('contract_validity_period_to', models.CharField(blank=True, max_length=20, null=True)),
                ('special_price_setting_not_allowed_flag', models.BooleanField(default=False)),
                ('mark_up_rate', models.CharField(blank=True, max_length=100, null=True)),
                ('individual_item_surcharge', models.CharField(blank=True, max_length=100, null=True)),
                ('rank_difference', models.CharField(blank=True, max_length=100, null=True)),
                ('set_sales_price', models.CharField(blank=True, max_length=100, null=True)),
                ('six_level_rank_setting', models.CharField(blank=True, max_length=100, null=True)),
                ('six_level_rank_set_sales_price', models.CharField(blank=True, max_length=100, null=True)),
                ('six_level_rank_mark_up_rate', models.CharField(blank=True, max_length=100, null=True)),
                ('producer_price_case', models.CharField(blank=True, max_length=100, null=True)),
                ('producer_price_individual', models.CharField(blank=True, max_length=100, null=True)),
                ('wholesale_price_case', models.CharField(blank=True, max_length=100, null=True)),
                ('wholesale_price_individual', models.CharField(blank=True, max_length=100, null=True)),
                ('retail_price_case', models.CharField(blank=True, max_length=100, null=True)),
                ('retail_price_individual', models.CharField(blank=True, max_length=100, null=True)),
                ('contract_purchase_price1_case', models.CharField(blank=True, max_length=100, null=True)),
                ('contract_purchase_price1_individual', models.CharField(blank=True, max_length=100, null=True)),
                ('contract_purchase_price2_case', models.CharField(blank=True, max_length=100, null=True)),
                ('contract_purchase_price2_individual', models.CharField(blank=True, max_length=100, null=True)),
                ('final_purchase_price_case', models.CharField(blank=True, max_length=100, null=True)),
                ('final_purchase_price_individual', models.CharField(blank=True, max_length=100, null=True)),
                ('deposit_for_empty_case', models.CharField(blank=True, max_length=100, null=True)),
                ('deposit_for_empty_individual', models.CharField(blank=True, max_length=100, null=True)),
                ('bonus_unit_price_case', models.CharField(blank=True, max_length=100, null=True)),
                ('bonus_unit_price_individual', models.CharField(blank=True, max_length=100, null=True)),
                ('transaction_start_date', models.CharField(blank=True, max_length=255, null=True)),
                ('promotional_price_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('promotional_price_classification_name', models.CharField(blank=True, max_length=255, null=True)),
                ('promotional_price_memo', models.TextField(blank=True, null=True)),
                ('promotional_price_case', models.CharField(blank=True, max_length=100, null=True)),
                ('promotional_price_individual', models.CharField(blank=True, max_length=100, null=True)),
                ('shipping_restriction_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('shipping_restriction_classification_name', models.CharField(blank=True, max_length=255, null=True)),
                ('volume_unit_price_setting_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('volume_unit_price_setting_classification_name', models.CharField(blank=True, max_length=255, null=True)),
                ('shipping_fee_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('shipping_fee_classification_name', models.CharField(blank=True, max_length=255, null=True)),
                ('shipping_fee_amount', models.CharField(blank=True, max_length=25, null=True)),
                ('type', models.CharField(blank=True, max_length=100, null=True)),
                ('production_area', models.CharField(blank=True, max_length=255, null=True)),
                ('ingredient_origin_name', models.CharField(blank=True, max_length=255, null=True)),
                ('free_comment', models.TextField(blank=True, null=True)),
                ('inventory_management_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('inventory_management_classification_name', models.CharField(blank=True, max_length=255, null=True)),
                ('maximum_stock_quantity', models.CharField(blank=True, max_length=100, null=True)),
                ('minimum_stock_quantity', models.CharField(blank=True, max_length=100, null=True)),
                ('designated_shipping_date', models.CharField(blank=True, max_length=20, null=True)),
                ('brand', models.CharField(blank=True, max_length=255, null=True)),
                ('empty_container_code', models.CharField(blank=True, max_length=100, null=True)),
                ('return_handling_classification_for_empty', models.CharField(blank=True, max_length=100, null=True)),
                ('registration_requester_code', models.CharField(blank=True, max_length=100, null=True)),
                ('sales_restriction_classification_for_specific_customers', models.CharField(blank=True, max_length=100, null=True)),
                ('sales_restriction_classification_name_for_specific_customers', models.CharField(blank=True, max_length=255, null=True)),
                ('applicable_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('applicable_classification_name', models.CharField(blank=True, max_length=255, null=True)),
                ('product_display_classification', models.CharField(blank=True, max_length=100, null=True)),
                ('product_display_classification_name', models.CharField(blank=True, max_length=255, null=True)),
                ('previous_day_delivery_from', models.CharField(blank=True, max_length=20, null=True)),
                ('previous_day_delivery_to', models.CharField(blank=True, max_length=20, null=True)),
                ('pallet_adjustment_early_morning_arrival_target_flag', models.CharField(blank=True, max_length=20, null=True)),
                ('pallet_adjustment_standard_quantity', models.CharField(blank=True, max_length=20, null=True)),
                ('infomart_transmission_flag', models.BooleanField(default=False)),
                ('prohibited_use_flag', models.CharField(blank=True, max_length=10, null=True)),
                ('location_code', models.CharField(blank=True, max_length=100, null=True)),
                ('location_name', models.CharField(blank=True, max_length=25, null=True)),
                ('print_flag', models.CharField(blank=True, max_length=20, null=True)),
                ('registration_datetime', models.CharField(blank=True, max_length=20, null=True)),
                ('registering_user', models.CharField(blank=True, max_length=20, null=True)),
                ('registering_user_name', models.CharField(blank=True, max_length=255, null=True)),
                ('registration_process', models.CharField(blank=True, max_length=20, null=True)),
                ('update_datetime', models.CharField(blank=True, max_length=20, null=True)),
                ('updating_user', models.CharField(blank=True, max_length=20, null=True)),
                ('updating_user_name', models.CharField(blank=True, max_length=255, null=True)),
                ('update_process', models.CharField(blank=True, max_length=20, null=True)),
            ],
            options={
                'verbose_name_plural': 'ProductItemMasters',
                'db_table': 'product_item_masters',
            },
        ),
        migrations.RemoveField(
            model_name='productitem',
            name='maker_name',
        ),
        migrations.AddField(
            model_name='productitem',
            name='sync_product_code',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AlterField(
            model_name='productitem',
            name='product_code',
            field=models.CharField(max_length=15, unique=True),
        ),
    ]
