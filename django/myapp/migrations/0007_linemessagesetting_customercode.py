# Generated by Django 5.2.1 on 2025-08-21 03:44

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('myapp', '0006_alter_productitem_product_packing'),
    ]

    operations = [
        migrations.CreateModel(
            name='LineMessageSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('delivery_datetime', models.DateTimeField(null=True, verbose_name='配信日付')),
                ('management_title', models.CharField(blank=True, max_length=255, null=True, verbose_name='管理用タイトル')),
                ('format_type', models.CharField(blank=True, max_length=255, null=True, verbose_name='フォーマット')),
                ('product_code', models.CharField(max_length=50, verbose_name='商品コード')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='CustomerCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, verbose_name='取引先コード')),
                ('line_message', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customer_codes', to='myapp.linemessagesetting')),
            ],
        ),
    ]
