from django.utils import timezone
from django.db import models
import csv
from django.db import transaction
from django.db.models import Index
import jaconv
import unicodedata
import re

def normalize_japanese(text):
    """
    Normalize Japanese text for search purposes:
    1. Convert to NFKC form (normalize character width)
    2. Convert hiragana to katakana
    3. Convert half-width katakana to full-width
    4. Remove spaces and special characters
    """
    if not text:
        return ""

    # Normalize character width (NFKC form)
    text = unicodedata.normalize('NFKC', text)

    # Convert hiragana to katakana
    text = jaconv.hira2kata(text)

    # Convert half-width katakana to full-width
    text = jaconv.h2z(text, kana=True, ascii=False, digit=False)

    # Remove spaces and special characters
    text = re.sub(r'[\s・、。「」\(\)（）\[\]［］\{\}｛｝:：;；,，\.．]', '', text)

    return text

class Category(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)

    class Meta:
        db_table = 'categories'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.name

class ProductItemMaster(models.Model):
    product_code = models.CharField(max_length=15 , unique=True)
    parent_product_code = models.TextField(max_length=60, null=False)
    product_name = models.CharField(max_length=255, null=False)
    product_name_kana = models.CharField(max_length=255, null=False)
    finet_product_name_kana = models.CharField(max_length=255, null=False)
    product_rank = models.CharField(max_length=10, null=False)
    capacity = models.FloatField()
    capacity_ml = models.CharField(max_length=10, null=False)
    unit = models.CharField(max_length=10, null=False)
    quantity_per_case = models.IntegerField()
    category_large = models.IntegerField()
    category_middle = models.IntegerField()
    category_small = models.IntegerField()
    product_group_name = models.CharField(max_length=100, null=False)
    foodnavi_product_code = models.CharField(max_length=20, null=False)
    input_category_code = models.IntegerField()
    input_category_name = models.CharField(max_length=100, null=False)
    maker_code = models.IntegerField()
    maker_name = models.CharField(max_length=100, null=False)
    supplier_code_1 = models.IntegerField()
    supplier_name_1 = models.CharField(max_length=100, null=False)
    order_unit_1 = models.IntegerField()
    combined_1 = models.IntegerField()
    jan_code = models.CharField(max_length=20, null=False)
    official_flg = models.CharField(max_length=200, null=False)
    product_catchcopy = models.IntegerField()

    class Meta:
        db_table = 'product_item_master'
        verbose_name_plural = 'ProductItemMaster'

    def __str__(self):
        return self.product_name

    @classmethod
    def import_from_csv(cls, csv_file_path):
        """
        Import data from a CSV file into ProductItemMaster model

        Args:
            csv_file_path (str): Path to the CSV file

        Returns:
            tuple: (success_count, error_count, error_messages)
        """


        success_count = 0
        error_count = 0
        error_messages = []

        try:
            with open(csv_file_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)

                # Validate header
                required_fields = [
                    'product_code', 'parent_product_code', 'product_name',
                    'product_name_kana', 'finet_product_name_kana', 'product_rank',
                    'capacity', 'capacity_ml', 'unit', 'quantity_per_case'
                ]

                for field in required_fields:
                    if field not in reader.fieldnames:
                        return 0, 1, [f"CSV file is missing required field: {field}"]

                # Process rows
                with transaction.atomic():
                    for row_num, row in enumerate(reader, start=2):
                        try:
                            # Create or update the product item
                            product_item, created = cls.objects.update_or_create(
                                product_code=row['product_code'],
                                defaults={
                                    'parent_product_code': row['parent_product_code'],
                                    'product_name': row['product_name'],
                                    'product_name_kana': row['product_name_kana'],
                                    'finet_product_name_kana': row['finet_product_name_kana'],
                                    'product_rank': row['product_rank'],
                                    'capacity': row['capacity'],
                                    'capacity_ml': row['capacity_ml'],
                                    'unit': row['unit'],
                                    'quantity_per_case': int(row['quantity_per_case']) if row['quantity_per_case'] else 0,
                                    'category_large': int(row['category_large']) if row.get('category_large') else 0,
                                    'category_middle': int(row['category_middle']) if row.get('category_middle') else 0,
                                    'category_small': int(row['category_small']) if row.get('category_small') else 0,
                                    'product_group_name': row.get('product_group_name', ''),
                                    'foodnavi_product_code': row.get('foodnavi_product_code', ''),
                                    'input_category_code': int(row['input_category_code']) if row.get('input_category_code') else 0,
                                    'input_category_name': row.get('input_category_name', ''),
                                    'maker_code': int(row['maker_code']) if row.get('maker_code') else 0,
                                    'maker_name': row.get('maker_name', ''),
                                    'supplier_code_1': int(row['supplier_code_1']) if row.get('supplier_code_1') else 0,
                                    'supplier_name_1': row.get('supplier_name_1', ''),
                                    'order_unit_1': int(row['order_unit_1']) if row.get('order_unit_1') else 0,
                                    'combined_1': int(row['combined_1']) if row.get('combined_1') else 0,
                                    'jan_code': int(row['jan_code']) if row.get('jan_code') else 0,
                                    'official_flg': row.get('official_flg', ''),
                                    'product_catchcopy': int(row['product_catchcopy']) if row.get('product_catchcopy') else 0,
                                }
                            )
                            success_count += 1
                        except Exception as e:
                            error_count += 1
                            error_messages.append(f"Error on row {row_num}: {str(e)}")

        except Exception as e:
            return 0, 1, [f"Failed to process CSV file: {str(e)}"]

        return success_count, error_count, error_messages
class ProductItem(models.Model):
    company_code = models.CharField(max_length=3, null=False)
    product_code = models.CharField(max_length=15, unique=True)
    product_name = models.CharField(max_length=200, null=False)
    product_kana = models.CharField(max_length=200, null=False)
    product_standard = models.CharField(max_length=20, null=False)
    in_numbers = models.DecimalField(max_digits=15, decimal_places=2, default=0.2, null=False)
    product_packing = models.CharField(max_length=20, null=True)
    unit_name = models.CharField(max_length=10, null=False)
    category_code1 = models.CharField(max_length=12, null=False)
    category_code2 = models.CharField(max_length=12, null=False)
    category_code3 = models.CharField(max_length=12, null=False)
    category_code4 = models.CharField(max_length=12, null=False)
    category_code5 = models.CharField(max_length=12, null=False)
    description1 = models.TextField(null=True)
    description2 = models.TextField(null=True)
    price_switch_day = models.DateField(default='2050-12-31', null=False)
    rank_category_1 = models.IntegerField(default=0, null=False)
    rank_category_2 = models.IntegerField(default=0, null=False)
    lead_time = models.IntegerField(null=False)
    measurement_division = models.IntegerField(default=0, null=False)
    decimal_point_permission_division = models.IntegerField(default=0, null=False)
    tax_type = models.IntegerField(default=0, null=False)
    tax_rate = models.IntegerField(default=0, null=False)
    is_visible = models.BooleanField(default=True, null=False)
    created_at = models.DateTimeField(null=False)
    created_user = models.CharField(max_length=20, null=False)
    updated_at = models.DateTimeField(null=False)
    updated_user = models.CharField(max_length=20, null=False)
    product_name_search = models.CharField(max_length=200, default="")
    product_kana_search = models.CharField(max_length=200, default="")
    is_check_sync = models.BooleanField(default=True, null=False)
    check_sync_at = models.DateTimeField(null=True, blank=True)
    sync_product_code = models.CharField(max_length=20, blank=True, null=True,)

    class Meta:
        db_table = 'product_items'

    def __str__(self):
        return f"{self.product_code} - {self.product_name}"

    @classmethod
    def import_from_csv(cls, csv_file_path):
        success_count = 0
        error_count = 0
        error_messages = []

        try:
            with open(csv_file_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                # Process rows
                with transaction.atomic():
                    for row_num, row in enumerate(reader, start=3):
                        try:
                            # Create or update the product item
                            product, created = cls.objects.update_or_create(
                                product_code=row['product_code'],
                                defaults={
                                    'company_code': row['company_code'],
                                    'product_name': row['product_name'],
                                    'product_kana': row['product_kana'],
                                    'product_standard': row['product_standard'],
                                    'in_numbers': row['in_numbers'],
                                    'product_packing': row['product_packing'],
                                    'unit_name': row['unit_name'],
                                    'category_code1': row['category_code1'],
                                    'category_code2': row['category_code2'],
                                    'category_code3': row['category_code3'],
                                    'category_code4': row['category_code4'],
                                    'category_code5': row['category_code5'],
                                    'description1': row['description1'],
                                    'description2': row['description2'],
                                    'price_switch_day': row['price_switch_day'],
                                    'rank_category_1': row['rank_category_1'],
                                    'rank_category_2': row['rank_category_2'],
                                    'lead_time': row['lead_time'],
                                    'measurement_division': row['measurement_division'],
                                    'decimal_point_permission_division': row['decimal_point_permission_division'],
                                    'tax_type': row['tax_type'],
                                    'tax_rate': row['tax_rate'],
                                    'is_visible': row['is_visible'],
                                    'created_at': row['created_at'],
                                    'created_user': row['created_user'],
                                    'updated_at': row['updated_at'],
                                    'updated_user': row['updated_user'],
                                    'is_check_sync': row['is_check_sync'],
                                    'product_name_search': normalize_japanese(row['product_name']),
                                    'product_kana_search': normalize_japanese(row['product_kana']),
                                }
                            )
                            success_count += 1
                        except Exception as e:
                            error_count += 1
                            error_messages.append(f"Error on row {row_num}: {str(e)}")

        except Exception as e:
            return 0, 1, [f"Failed to process CSV file: {str(e)}"]

        return success_count, error_count, error_messages

class ProductItemMapping(models.Model):
    product_item_master_id = models.CharField(max_length=100, null=False)
    product_items_id = models.TextField(max_length=100, null=False)
    sync_type = models.IntegerField(default=0)
    created_at = models.DateTimeField(default=timezone.now)
    deleted_at = models.DateTimeField(null=False)
    is_deleted = models.BooleanField(null=False)

    class Meta:
        db_table = 'product_item_mappings'
        verbose_name_plural = 'ProductItemMapping'

    def __str__(self):
        return self.name


class ProductItemMasters(models.Model):
    product_code = models.CharField(max_length=20, unique=True)
    parent_product_code = models.CharField(max_length=20, blank=True, null=True)
    product_name = models.CharField(max_length=255)
    product_name_kana = models.CharField(max_length=255, blank=True, null=True)
    product_name_for_finet_kana = models.CharField(max_length=255, blank=True, null=True)
    product_rank = models.CharField(max_length=50, blank=True, null=True)
    capacity = models.CharField(max_length=20,blank=True, null=True)
    capacity_ml = models.CharField(max_length=20,blank=True, null=True)
    unit = models.CharField(max_length=50, blank=True, null=True)
    number_of_units_per_pack = models.CharField(max_length=50,blank=True, null=True)

    major_category = models.CharField(max_length=100, blank=True, null=True)
    middle_category = models.CharField(max_length=100, blank=True, null=True)
    subcategory = models.CharField(max_length=100, blank=True, null=True)
    product_group_name = models.CharField(max_length=100, blank=True, null=True)

    food_navi_product_code = models.CharField(max_length=20, blank=True, null=True)
    input_classification_code = models.CharField(max_length=20, blank=True, null=True)
    input_classification_name = models.CharField(max_length=20, blank=True, null=True)

    manufacturer_code = models.CharField(max_length=20, blank=True, null=True)
    manufacturer_name = models.CharField(max_length=255, blank=True, null=True)

    supplier_code_1 = models.CharField(max_length=20, blank=True, null=True)
    supplier_1 = models.CharField(max_length=255, blank=True, null=True)
    order_unit_1 = models.CharField(max_length=100, blank=True, null=True)
    set_1 = models.CharField(max_length=100, blank=True, null=True)

    supplier_code_2 = models.CharField(max_length=20, blank=True, null=True)
    supplier_name_2 = models.CharField(max_length=255, blank=True, null=True)
    order_unit_2 = models.CharField(max_length=100, blank=True, null=True)
    set_2 = models.CharField(max_length=100, blank=True, null=True)

    supplier = models.CharField(max_length=255, blank=True, null=True)
    transaction_partner_code = models.CharField(max_length=100, blank=True, null=True)
    transaction_partner_name = models.CharField(max_length=255, blank=True, null=True)

    empty_container_supplier_code = models.CharField(max_length=100, blank=True, null=True)

    tax_code = models.CharField(max_length=20, blank=True, null=True)
    tax_name = models.CharField(max_length=255, blank=True, null=True)

    handling_category = models.CharField(max_length=100, blank=True, null=True)
    handling_category_name = models.CharField(max_length=255, blank=True, null=True)

    miscellaneous_category = models.CharField(max_length=100, blank=True, null=True)
    miscellaneous_category_name = models.CharField(max_length=255, blank=True, null=True)

    market_price_product_category = models.CharField(max_length=100, blank=True, null=True)
    market_price_product_category_name = models.CharField(max_length=255, blank=True, null=True)

    container_classification = models.CharField(max_length=100, blank=True, null=True)
    logistics_classification = models.CharField(max_length=100, blank=True, null=True)

    variable_weight_product_category = models.CharField(max_length=100, blank=True, null=True)
    variable_weight_product_category_name = models.CharField(max_length=255, blank=True, null=True)

    stocked_item_start_date = models.CharField(max_length=20,blank=True, null=True)
    stocked_item_end_date = models.CharField(max_length=20,blank=True, null=True)
    jan_code_presence_classification = models.CharField(max_length=100, blank=True, null=True)
    jan_code_presence_classification_name = models.CharField(max_length=255, blank=True, null=True)
    case_jan_code = models.CharField(max_length=25, blank=True, null=True)
    individual_item_jan_code = models.CharField(max_length=25, blank=True, null=True)
    itf_code = models.CharField(max_length=20, blank=True, null=True)
    sdp_code = models.CharField(max_length=20, blank=True, null=True)
    jan_code_for_intage_use = models.CharField(max_length=100, blank=True, null=True)
    jan_code_switch_date = models.CharField(max_length=20,blank=True, null=True)
    old_case_jan_code = models.CharField(max_length=100, blank=True, null=True)
    old_individual_item_jan_code = models.CharField(max_length=100, blank=True, null=True)

    weight_per_case = models.CharField(max_length=100,blank=True, null=True)
    individual_item_weight = models.CharField(max_length=100,blank=True, null=True)

    warehouse_classification = models.CharField(max_length=100, blank=True, null=True)
    warehouse_classification_name = models.CharField(max_length=255, blank=True, null=True)
    storage_classification = models.CharField(max_length=100, blank=True, null=True)
    storage_classification_name = models.CharField(max_length=255, blank=True, null=True)
    location = models.CharField(max_length=255, blank=True, null=True)

    case_depth = models.CharField(max_length=100,blank=True, null=True)
    case_width = models.CharField(max_length=100,blank=True, null=True)
    case_height = models.CharField(max_length=100,blank=True, null=True)
    individual_item_depth = models.CharField(max_length=100,blank=True, null=True)
    individual_item_width = models.CharField(max_length=100,blank=True, null=True)
    individual_item_height = models.CharField(max_length=100,blank=True, null=True)

    case_rollover_permission_classification = models.CharField(max_length=100, blank=True, null=True)
    case_rollover_permission_classification_name = models.CharField(max_length=255, blank=True, null=True)

    empty_container_classification = models.CharField(max_length=100, blank=True, null=True)
    empty_container_classification_name = models.CharField(max_length=255, blank=True, null=True)

    returnable_empty_container_unit_price_classification = models.CharField(max_length=100, blank=True, null=True)
    returnable_empty_container_unit_price_classification_name = models.CharField(max_length=255, blank=True, null=True)

    deposit_code = models.CharField(max_length=100, blank=True, null=True)
    deposit_name = models.CharField(max_length=255, blank=True, null=True)

    expiry_date = models.CharField(max_length=100,blank=True, null=True)
    shipment_deadline = models.CharField(max_length=100,blank=True, null=True)
    shipment_deadline_warning_classification = models.CharField(max_length=100, blank=True, null=True)
    shipment_deadline_warning_classification_name = models.CharField(max_length=255, blank=True, null=True)

    long_term_restocking_uncertain_flag = models.CharField(max_length=5 , blank=True, null=True)
    discontinuation_classification = models.CharField(max_length=100, blank=True, null=True)
    discontinuation_classification_name = models.CharField(max_length=255, blank=True, null=True)
    discontinuation_year_month = models.CharField(max_length=20, blank=True, null=True)  # e.g., "2024-12"
    discontinuation_date = models.CharField(max_length=100,blank=True, null=True)
    discontinuation_timing_classification = models.CharField(max_length=100, blank=True, null=True)
    reason_for_discontinuation = models.TextField(blank=True, null=True)
    discontinuation_status_classification = models.CharField(max_length=100, blank=True, null=True)
    discontinuation_status_classification_name = models.CharField(max_length=255, blank=True, null=True)

    substitute_product_code = models.CharField(max_length=100, blank=True, null=True)
    substitute_product_name = models.CharField(max_length=255, blank=True, null=True)

    recommended_product_code_1 = models.CharField(max_length=100, blank=True, null=True)
    recommended_product_name_1 = models.CharField(max_length=255, blank=True, null=True)
    recommended_product_code_2 = models.CharField(max_length=100, blank=True, null=True)
    recommended_product_name_2 = models.CharField(max_length=255, blank=True, null=True)
    recommended_product_code_3 = models.CharField(max_length=100, blank=True, null=True)
    recommended_product_name_3 = models.CharField(max_length=255, blank=True, null=True)
    sales_recommendation_level = models.CharField(max_length=20,blank=True, null=True)
    discontinued_product_code = models.CharField(max_length=20, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)

    alcohol_content = models.CharField(max_length=100,blank=True, null=True)
    extract_content = models.CharField(max_length=100,blank=True, null=True)

    contract_condition_1 = models.TextField(blank=True, null=True)
    contract_condition_2 = models.TextField(blank=True, null=True)
    contract_validity_period_from = models.CharField(max_length=20,blank=True, null=True)
    contract_validity_period_to = models.CharField(max_length=20,blank=True, null=True)

    special_price_setting_not_allowed_flag = models.BooleanField(default=False)
    mark_up_rate = models.CharField(max_length=100,blank=True, null=True)
    individual_item_surcharge = models.CharField(max_length=100,blank=True, null=True)
    rank_difference = models.CharField(max_length=100, blank=True, null=True)
    set_sales_price = models.CharField(max_length=100,blank=True, null=True)

    six_level_rank_setting = models.CharField(max_length=100, blank=True, null=True)
    six_level_rank_set_sales_price = models.CharField(max_length=100,blank=True, null=True)
    six_level_rank_mark_up_rate = models.CharField(max_length=100,blank=True, null=True)

    producer_price_case = models.CharField(max_length=100,blank=True, null=True)
    producer_price_individual = models.CharField(max_length=100,blank=True, null=True)
    wholesale_price_case = models.CharField(max_length=100,blank=True, null=True)
    wholesale_price_individual = models.CharField(max_length=100,blank=True, null=True)
    retail_price_case = models.CharField(max_length=100,blank=True, null=True)
    retail_price_individual = models.CharField(max_length=100,blank=True, null=True)

    contract_purchase_price1_case = models.CharField(max_length=100,blank=True, null=True)
    contract_purchase_price1_individual = models.CharField(max_length=100,blank=True, null=True)
    contract_purchase_price2_case = models.CharField(max_length=100,blank=True, null=True)
    contract_purchase_price2_individual = models.CharField(max_length=100,blank=True, null=True)

    final_purchase_price_case = models.CharField(max_length=100,blank=True, null=True)
    final_purchase_price_individual = models.CharField(max_length=100,blank=True, null=True)

    deposit_for_empty_case = models.CharField(max_length=100,blank=True, null=True)
    deposit_for_empty_individual = models.CharField(max_length=100,blank=True, null=True)

    bonus_unit_price_case = models.CharField(max_length=100,blank=True, null=True)
    bonus_unit_price_individual = models.CharField(max_length=100,blank=True, null=True)

    transaction_start_date = models.CharField(max_length=255,blank=True, null=True)

    promotional_price_classification = models.CharField(max_length=100, blank=True, null=True)
    promotional_price_classification_name = models.CharField(max_length=255, blank=True, null=True)
    promotional_price_memo = models.TextField(blank=True, null=True)
    promotional_price_case = models.CharField(max_length=100, blank=True, null=True)
    promotional_price_individual = models.CharField(max_length=100, blank=True, null=True)

    shipping_restriction_classification = models.CharField(max_length=100, blank=True, null=True)
    shipping_restriction_classification_name = models.CharField(max_length=255, blank=True, null=True)

    volume_unit_price_setting_classification = models.CharField(max_length=100, blank=True, null=True)
    volume_unit_price_setting_classification_name = models.CharField(max_length=255, blank=True, null=True)

    shipping_fee_classification = models.CharField(max_length=100, blank=True, null=True)
    shipping_fee_classification_name = models.CharField(max_length=255, blank=True, null=True)
    shipping_fee_amount = models.CharField(max_length=25 ,blank=True, null=True)

    type = models.CharField(max_length=100, blank=True, null=True)
    production_area = models.CharField(max_length=255, blank=True, null=True)
    ingredient_origin_name = models.CharField(max_length=255, blank=True, null=True)
    free_comment = models.TextField(blank=True, null=True)

    inventory_management_classification = models.CharField(max_length=100, blank=True, null=True)
    inventory_management_classification_name = models.CharField(max_length=255, blank=True, null=True)
    maximum_stock_quantity = models.CharField(max_length=100,blank=True, null=True)
    minimum_stock_quantity = models.CharField(max_length=100,blank=True, null=True)

    designated_shipping_date = models.CharField(max_length=20,blank=True, null=True)
    brand = models.CharField(max_length=255, blank=True, null=True)
    empty_container_code = models.CharField(max_length=100, blank=True, null=True)
    return_handling_classification_for_empty = models.CharField(max_length=100, blank=True, null=True)
    registration_requester_code = models.CharField(max_length=100, blank=True, null=True)

    sales_restriction_classification_for_specific_customers = models.CharField(max_length=100, blank=True, null=True)
    sales_restriction_classification_name_for_specific_customers = models.CharField(max_length=255, blank=True,
                                                                                    null=True)

    applicable_classification = models.CharField(max_length=100, blank=True, null=True)
    applicable_classification_name = models.CharField(max_length=255, blank=True, null=True)

    product_display_classification = models.CharField(max_length=100, blank=True, null=True)
    product_display_classification_name = models.CharField(max_length=255, blank=True, null=True)

    previous_day_delivery_from = models.CharField(max_length=20,blank=True, null=True)
    previous_day_delivery_to = models.CharField(max_length=20,blank=True, null=True)

    pallet_adjustment_early_morning_arrival_target_flag = models.CharField(max_length=20,blank=True, null=True)
    pallet_adjustment_standard_quantity = models.CharField(max_length=20,blank=True, null=True)

    infomart_transmission_flag = models.BooleanField(default=False)
    prohibited_use_flag = models.CharField(max_length=10, blank=True, null=True)

    location_code = models.CharField(max_length=100, blank=True, null=True)
    location_name = models.CharField(max_length=25, blank=True, null=True)

    print_flag = models.CharField(max_length=20,blank=True, null=True)

    registration_datetime = models.CharField(max_length=20,blank=True, null=True) #FP
    registering_user = models.CharField(max_length=20, blank=True, null=True)
    registering_user_name = models.CharField(max_length=255, blank=True, null=True)
    registration_process = models.CharField(max_length=20, blank=True, null=True)

    update_datetime = models.CharField(max_length=20,blank=True, null=True)
    updating_user = models.CharField(max_length=20, blank=True, null=True)
    updating_user_name = models.CharField(max_length=255, blank=True, null=True)
    update_process = models.CharField(max_length=20, blank=True, null=True)
    product_name_search = models.CharField(max_length=200, default="")
    product_kana_search = models.CharField(max_length=200, default="")
    class Meta:
        db_table = 'product_item_masters'
        verbose_name_plural = 'ProductItemMasters'

    def __str__(self):
        return self.product_code

    @classmethod
    def import_from_csv(cls, csv_file_path):

        success_count = 0
        error_count = 0
        error_messages = []

        try:
            with open(csv_file_path, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                # Process rows
                with transaction.atomic():
                    for row_num, row in enumerate(reader, start=3):
                        try:
                            # Create or update the product item
                            product, created = cls.objects.update_or_create(
                                product_code=row['product_code'],
                                defaults={
                                    'parent_product_code': row['parent_product_code'],
                                    'product_name': row['product_name'],
                                    'product_name_kana': row['product_name_kana'],
                                    'product_name_for_finet_kana': row['product_name_for_finet_kana'],
                                    'product_rank': row['product_rank'],
                                    'capacity': row['capacity'],
                                    'capacity_ml': row['capacity_ml'],
                                    'unit': row['unit'],
                                    'number_of_units_per_pack': row['number_of_units_per_pack'],
                                    'major_category': row['major_category'],
                                    'middle_category': row['middle_category'],
                                    'subcategory': row['subcategory'],
                                    'product_group_name': row['product_group_name'],
                                    'food_navi_product_code': row['food_navi_product_code'],
                                    'input_classification_code': row['input_classification_code'],
                                    'input_classification_name': row['input_classification_name'],
                                    'manufacturer_code': row['manufacturer_code'],
                                    'manufacturer_name': row['manufacturer_name'],
                                    'supplier_code_1': row['supplier_code_1'],
                                    'supplier_1': row['supplier_1'],
                                    'order_unit_1': row['order_unit_1'],
                                    'set_1': row['set_1'],
                                    'supplier_code_2': row['supplier_code_2'],
                                    'supplier_name_2': row['supplier_name_2'],
                                    'order_unit_2': row['order_unit_2'],
                                    'set_2': row['set_2'],
                                    'supplier': row['supplier'],
                                    'transaction_partner_code': row['transaction_partner_code'],
                                    'transaction_partner_name': row['transaction_partner_name'],
                                    'empty_container_supplier_code': row['empty_container_supplier_code'],
                                    'tax_code': row['tax_code'],
                                    'tax_name': row['tax_name'],
                                    'handling_category': row['handling_category'],
                                    'handling_category_name': row['handling_category_name'],
                                    'miscellaneous_category': row['miscellaneous_category'],
                                    'miscellaneous_category_name': row['miscellaneous_category_name'],
                                    'market_price_product_category': row['market_price_product_category'],
                                    'market_price_product_category_name': row['market_price_product_category_name'],
                                    'container_classification': row['container_classification'],
                                    'logistics_classification': row['logistics_classification'],
                                    'variable_weight_product_category': row['variable_weight_product_category'],
                                    'variable_weight_product_category_name': row['variable_weight_product_category_name'],
                                    'stocked_item_start_date': row['stocked_item_start_date'],
                                    'stocked_item_end_date': row['stocked_item_end_date'],
                                    'jan_code_presence_classification': row['jan_code_presence_classification'],
                                    'jan_code_presence_classification_name': row['jan_code_presence_classification_name'],
                                    'case_jan_code': row['case_jan_code'],
                                    'individual_item_jan_code': row['individual_item_jan_code'],
                                    'itf_code': row['itf_code'],
                                    'sdp_code': row['sdp_code'],
                                    'jan_code_for_intage_use': row['jan_code_for_intage_use'],
                                    'jan_code_switch_date': row['jan_code_switch_date'],
                                    'old_case_jan_code': row['old_case_jan_code'],
                                    'old_individual_item_jan_code': row['old_individual_item_jan_code'],
                                    'weight_per_case': row['weight_per_case'],
                                    'individual_item_weight': row['individual_item_weight'],
                                    'warehouse_classification': row['warehouse_classification'],
                                    'warehouse_classification_name': row['warehouse_classification_name'],
                                    'storage_classification': row['storage_classification'],
                                    'storage_classification_name': row['storage_classification_name'],
                                    'location': row['location'],
                                    'case_depth': row['case_depth'],
                                    'case_width': row['case_width'],
                                    'case_height': row['case_height'],
                                    'individual_item_depth': row['individual_item_depth'],
                                    'individual_item_width': row['individual_item_width'],
                                    'individual_item_height': row['individual_item_height'],
                                    'case_rollover_permission_classification': row['case_rollover_permission_classification'],
                                    'case_rollover_permission_classification_name': row['case_rollover_permission_classification_name'],
                                    'empty_container_classification': row['empty_container_classification'],
                                    'empty_container_classification_name': row['empty_container_classification_name'],
                                    'returnable_empty_container_unit_price_classification': row['returnable_empty_container_unit_price_classification'],
                                    'returnable_empty_container_unit_price_classification_name': row['returnable_empty_container_unit_price_classification_name'],
                                    'deposit_code': row['deposit_code'],
                                    'deposit_name': row['deposit_name'],
                                    'expiry_date': row['expiry_date'],
                                    'shipment_deadline': row['shipment_deadline'],
                                    'shipment_deadline_warning_classification': row['shipment_deadline_warning_classification'],
                                    'shipment_deadline_warning_classification_name': row['shipment_deadline_warning_classification_name'],
                                    'long_term_restocking_uncertain_flag': row['long_term_restocking_uncertain_flag'],
                                    'discontinuation_classification': row['discontinuation_classification'],
                                    'discontinuation_classification_name': row['discontinuation_classification_name'],
                                    'discontinuation_year_month': row['discontinuation_year_month'],
                                    'discontinuation_date': row['discontinuation_date'],
                                    'discontinuation_timing_classification': row['discontinuation_timing_classification'],
                                    'reason_for_discontinuation': row['reason_for_discontinuation'],
                                    'discontinuation_status_classification': row['discontinuation_status_classification'],
                                    'discontinuation_status_classification_name': row['discontinuation_status_classification_name'],
                                    'substitute_product_code': row['substitute_product_code'],
                                    'substitute_product_name': row['substitute_product_name'],
                                    'recommended_product_code_1': row['recommended_product_code_1'],
                                    'recommended_product_name_1': row['recommended_product_name_1'],
                                    'recommended_product_code_2': row['recommended_product_code_2'],
                                    'recommended_product_name_2': row['recommended_product_name_2'],
                                    'recommended_product_code_3': row['recommended_product_code_3'],
                                    'recommended_product_name_3': row['recommended_product_name_3'],
                                    'sales_recommendation_level': row['sales_recommendation_level'],
                                    'discontinued_product_code': row['discontinued_product_code'],
                                    'notes': row['notes'],
                                    'alcohol_content': row['alcohol_content'],
                                    'extract_content': row['extract_content'],
                                    'contract_condition_1': row['contract_condition_1'],
                                    'contract_condition_2': row['contract_condition_2'],
                                    'contract_validity_period_from': row['contract_validity_period_from'],
                                    'contract_validity_period_to': row['contract_validity_period_to'],
                                    'special_price_setting_not_allowed_flag': row['special_price_setting_not_allowed_flag'],
                                    'mark_up_rate': row['mark_up_rate'],
                                    'individual_item_surcharge': row['individual_item_surcharge'],
                                    'rank_difference': row['rank_difference'],
                                    'set_sales_price': row['set_sales_price'],
                                    'six_level_rank_setting': row['six_level_rank_setting'],
                                    'six_level_rank_set_sales_price': row['six_level_rank_set_sales_price'],
                                    'six_level_rank_mark_up_rate': row['six_level_rank_mark_up_rate'],
                                    'producer_price_case': row['producer_price_case'],
                                    'producer_price_individual': row['producer_price_individual'],
                                    'wholesale_price_case': row['wholesale_price_case'],
                                    'wholesale_price_individual': row['wholesale_price_individual'],
                                    'retail_price_case': row['retail_price_case'],
                                    'retail_price_individual': row['retail_price_individual'],
                                    'contract_purchase_price1_case': row['contract_purchase_price1_case'],
                                    'contract_purchase_price1_individual': row['contract_purchase_price1_individual'],
                                    'contract_purchase_price2_case': row['contract_purchase_price2_case'],
                                    'contract_purchase_price2_individual': row['contract_purchase_price2_individual'],
                                    'final_purchase_price_case': row['final_purchase_price_case'],
                                    'final_purchase_price_individual': row['final_purchase_price_individual'],
                                    'deposit_for_empty_case': row['deposit_for_empty_case'],
                                    'deposit_for_empty_individual': row['deposit_for_empty_individual'],
                                    'bonus_unit_price_case': row['bonus_unit_price_case'],
                                    'bonus_unit_price_individual': row['bonus_unit_price_individual'],
                                    'transaction_start_date': row['transaction_start_date'],
                                    'promotional_price_classification': row['promotional_price_classification'],
                                    'promotional_price_classification_name': row['promotional_price_classification_name'],
                                    'promotional_price_memo': row['promotional_price_memo'],
                                    'promotional_price_case': row['promotional_price_case'],
                                    'promotional_price_individual': row['promotional_price_individual'],
                                    'shipping_restriction_classification': row['shipping_restriction_classification'],
                                    'shipping_restriction_classification_name': row['shipping_restriction_classification_name'],
                                    'volume_unit_price_setting_classification': row['volume_unit_price_setting_classification'],
                                    'volume_unit_price_setting_classification_name': row['volume_unit_price_setting_classification_name'],
                                    'shipping_fee_classification': row['shipping_fee_classification'],
                                    'shipping_fee_classification_name': row['shipping_fee_classification_name'],
                                    'shipping_fee_amount': row['shipping_fee_amount'],
                                    'type': row['type'],
                                    'production_area': row['production_area'],
                                    'ingredient_origin_name': row['ingredient_origin_name'],
                                    'free_comment': row['free_comment'],
                                    'inventory_management_classification': row['inventory_management_classification'],
                                    'inventory_management_classification_name': row['inventory_management_classification_name'],
                                    'maximum_stock_quantity': row['maximum_stock_quantity'],
                                    'minimum_stock_quantity': row['minimum_stock_quantity'],
                                    'designated_shipping_date': row['designated_shipping_date'],
                                    'brand': row['brand'],
                                    'empty_container_code': row['empty_container_code'],
                                    'return_handling_classification_for_empty': row['return_handling_classification_for_empty'],
                                    'registration_requester_code': row['registration_requester_code'],
                                    'sales_restriction_classification_for_specific_customers': row['sales_restriction_classification_for_specific_customers'],
                                    'sales_restriction_classification_name_for_specific_customers': row['sales_restriction_classification_name_for_specific_customers'],
                                    'applicable_classification': row['applicable_classification'],
                                    'applicable_classification_name': row['applicable_classification_name'],
                                    'product_display_classification': row['product_display_classification'],
                                    'product_display_classification_name': row['product_display_classification_name'],
                                    'previous_day_delivery_from': row['previous_day_delivery_from'],
                                    'previous_day_delivery_to': row['previous_day_delivery_to'],
                                    'pallet_adjustment_early_morning_arrival_target_flag': row['pallet_adjustment_early_morning_arrival_target_flag'],
                                    'pallet_adjustment_standard_quantity': row['pallet_adjustment_standard_quantity'],
                                    'infomart_transmission_flag': row['infomart_transmission_flag'],
                                    'prohibited_use_flag': row['prohibited_use_flag'],
                                    'location_code': row['location_code'],
                                    'location_name': row['location_name'],
                                    'print_flag': row['print_flag'],
                                    'registration_datetime': row['registration_datetime'],
                                    'registering_user': row['registering_user'],
                                    'registering_user_name': row['registering_user_name'],
                                    'registration_process': row['registration_process'],
                                    'update_datetime': row['update_datetime'],
                                    'updating_user': row['updating_user'],
                                    'updating_user_name': row['updating_user_name'],
                                    'update_process': row['update_process'],
                                    'product_name_search': normalize_japanese(row['product_name']),
                                    'product_kana_search': normalize_japanese(row['product_name_kana'])
                                }
                            )
                            success_count += 1
                        except Exception as e:
                            error_count += 1
                            error_messages.append(f"Error on row {row_num}: {str(e)}")

        except Exception as e:
            return 0, 1, [f"Failed to process CSV file: {str(e)}"]

        return success_count, error_count, error_messages
