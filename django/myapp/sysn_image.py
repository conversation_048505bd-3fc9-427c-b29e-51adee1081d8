import os
import requests

def call_url_syns_image(product_code_image, product_code,):
    base_url = os.getenv('URL_SMART_ORDER', 'http://localhost')
    url = f"{base_url}/001/admin/product/syns_image/{product_code_image}/{product_code}"
    print(f"Calling URL: {url}")

    try:
        response = requests.get(url, timeout=10)
        print("Status code:", response.status_code)
        print("Raw response text:", repr(response.text))

        if response.status_code == 200:
            if response.text.strip() == "":
                return {"error": "Empty response"}
            try:
                data = response.json()
                print("Parsed JSON:", data)
                return data
            except ValueError:
                return {"error": "Response is not valid JSON", "text": response.text}
        else:
            return {"error": f"HTTP {response.status_code}", "text": response.text}
    except requests.exceptions.RequestException as e:
        return {"error": str(e)}
