"""core URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from django.contrib.auth import views as auth_views
from django.views.generic import RedirectView
from django.contrib.auth.decorators import login_required
from myapp import views
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    # path('admin/', admin.site.urls),
    path('login/', views.sso_login,name='sso_login_by_token'),
    path('logout/', auth_views.LogoutView.as_view(next_page='login'), name='logout'),
    path('import-product-items/', views.import_product_items, name='import_product_items'),
    path('import-product-items-master/', views.import_product_item_masters, name='import_product_item_masters'),
    path('products/', views.product_list, name='product_list'),
    path('products/<int:product_id>/', views.product_detail, name='product_detail'),
    # Redirect root URL to login page if not authenticated, otherwise to product list
    path('', RedirectView.as_view(url='products/'), name='root'),
    path('api/products/<str:id>/sync-code/', views.UpdateSyncProductCodeAPIView.as_view(), name='update-sync-code'),
    path('api/sysn-product-info/', views.sysn_info_product, name='sysn-product-info'),
    path('line_dashboard/', views.line_dashboard, name='line_dashboard'),
    path('line_dashboard/create', views.create_message_line, name='create_message_line'),
]
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATICFILES_DIRS[0])

