{% extends "layout/base.html" %}
{% load static %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">[{{loginName}}]カタログ - 一覧ページ</h2>

    <!-- Search and Filter Form -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-6">
                    <div class="input-group">
                        <input type="text" name="search" class="form-control"
                               placeholder="商品検索 - 商品コード・商品名" value="{{ search_query }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <select name="is_check_sync" class="form-select">
                        <option value="">突合状況</option>
                           <option value="1" {% if is_check_sync|stringformat:"s" == "1" %}selected{% endif %}>突合済</option>
                            <option value="0" {% if is_check_sync|stringformat:"s" == "0" %}selected{% endif %}>未突合</option>
                    </select>

                </div>

                <div class="col-md-3  d-flex align-items-end">
                    <button class="btn btn-outline-secondary">検索</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Data Table -->
    <div class="card">

        <div class="table-responsive">
            <table class="table table-hover ">
                <thead>
                <tr>
                    <th class="text-center">選択</th>
                    <th>商品コード</th>
                    <th>商品名</th>
                    <th>規格</th>
                    <th>入数</th>
                    <th>登録日時</th>
                    <th>食品DB突合</th>
                    <th>突合日時</th>

                    <th class="text-center">アクション</th>
                </tr>
                </thead>
                <tbody>
                {% for product in products %}
                <tr>
                    <td class="text-center">
                        <div class="form-group">
                            <label>
                                <input class="form-check-input" type="checkbox">
                            </label>
                        </div>
                    </td>
                    <td>{{ product.product_code}}</td>

                    {% if product.name|length > 60 %}
                        <td>{{ product.name|slice:":60" }}</td>
                    {% else %}
                        <td>{{ product.product_name }}</td>
                    {% endif %}

                    <td>{{ product.product_standard }}</td>
                    <td>{{ product.in_numbers }}</td>
                    <td>{{ product.created_at_jp}}</td>

                    {% if product.is_check_sync %}
                        <td>突合済</td>
                    {% else %}
                        <td>未突合</td>
                    {% endif %}

                    {% if product.is_check_sync %}
                        <td>{{ product.check_sync_time_jp}}</td>
                    {% else %}
                        <td></td>
                    {% endif %}


                    <td class="text-center">
                        <a href="{% url 'product_detail' product.id %}" class="btn btn-outline-dark ">詳細</a>
                        <button
                                type="button"
                                class="btn btn-primary preview-btn"
                                data-bs-toggle="modal"
                                data-bs-target="#imagePreviewModal"
                                data-image-url="{% static 'image/fake4.png' %}"
                        >プレビュー
                        </button>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7" class="text-center">データがありません</td>
                </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->

        <div class="card-footer">
            <div class="d-flex justify-content-between align-items-center flex-wrap gap-2">
                {% if products.has_other_pages %}
                <!-- Page size select -->
                <form method="get" class="d-flex align-items-center">
                    <select name="page_size" id="page_size" class="form-select" onchange="this.form.submit()">
                        {% for size in sizes %}
                            <option value="{{ size }}" {% if page_size == size %}selected{% endif %}>{{ size }}</option>
                        {% endfor %}
                    </select>

                    <!-- Preserve other query params -->
                    {% if search_query %}
                    <input type="hidden" name="search" value="{{ search_query }}">
                    {% endif %}
                    {% if is_check_sync %}
                    <input type="hidden" name="is_check_sync" value="{{ is_check_sync }}">
                    {% endif %}
                    {% if sort_by %}
                    <input type="hidden" name="sort" value="{{ sort_by }}">
                    {% endif %}
                    {% if products.number %}
                    <input type="hidden" name="page" value="{{ products.number }}">
                    {% endif %}
                </form>

                <div class="d-flex justify-content-between align-items-center">
                    <div>
                    </div>
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center mb-0">
                            {% if products.has_previous %}
                            <li class="page-item">
                                <a class="page-link"
                                   href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if maker_filter %}&maker={{ maker_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if page_size %}&page_size={{ page_size }}{% endif %}"
                                   aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link"
                                   href="?page={{ products.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if maker_filter %}&maker={{ maker_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if page_size %}&page_size={{ page_size }}{% endif %}"
                                   aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% for i in page_range %}
                            {% if products.number == i %}
                            <li class="page-item active">
                                <a class="page-link" href="#">{{i}}</a>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link"
                                   href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if maker_filter %}&maker={{ maker_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if page_size %}&page_size={{ page_size }}{% endif %}">
                                    {{i}}</a>
                            </li>
                            {% endif %}
                            {% endfor %}

                            {% if products.has_next %}
                            <li class="page-item">
                                <a class="page-link"
                                   href="?page={{ products.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if maker_filter %}&maker={{ maker_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if page_size %}&page_size={{ page_size }}{% endif %}"
                                   aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link"
                                   href="?page={{ products.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if maker_filter %}&maker={{ maker_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if page_size %}&page_size={{ page_size }}{% endif %}"
                                   aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    <div>
                        <!-- Empty div for flex alignment -->
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="modal fade" id="imagePreviewModal" tabindex="-1" aria-labelledby="imagePreviewModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="閉じる"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="previewImage" src="" alt="プレビュー画像" class="img-fluid rounded">
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block extra_js%}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const previewButtons = document.querySelectorAll('.preview-btn');
        const previewImage = document.getElementById('previewImage');

        previewButtons.forEach(button => {
            button.addEventListener('click', function () {
                const imageUrl = this.getAttribute('data-image-url');
                previewImage.src = imageUrl;
            });
        });
    });
</script>
{% endblock %}