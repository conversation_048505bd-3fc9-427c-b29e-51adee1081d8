{% extends "layout/base.html" %}
{% block content %}
<div class="container">
    <h2>Import Product Items master from CSV</h2>
    
    {% if messages %}
    <div class="messages">
        {% for message in messages %}
        <div class="alert {% if message.tags == 'error' %}alert-danger{% elif message.tags == 'warning' %}alert-warning{% else %}alert-success{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <div class="card">
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                {{ form.as_p }}
                <button type="submit" class="btn btn-primary">Upload and Import</button>
            </form>
        </div>
    </div>
</div>
{% endblock %}
