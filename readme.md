# Deploy Django, Nginx and MySql with Docker Compose

This repository contains code to deploy Django, Nginx and MySql containers with Docker Compose.

Nginx will serve web requests on port 80. For serving Django application running on port 8000 , we are using Gunicorn.

# Usage

After cloning the repository Run

```
Build the image : docker compose build

Run the containers : docker compose up

Close the containers : docker compose down

exec run migrage  : docker exec django_cont python manage.py migrate

exec run migrage  : docker exec django_cont python manage.py createsuperuser

create super user : docker exec -it django_cont python manage.py createsuperuser
```
